import os
import re
import sys
import pandas as pd
from tabulate import tabulate

def count_tokens_in_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            tokens = re.findall(r'\w+|[^\w\s]', content, re.UNICODE)
            return len(tokens)
    except Exception as e:
        print(f"无法读取文件 {file_path}，错误：{e}")
        return 0

def get_file_type(filename):
    ext = os.path.splitext(filename)[1].lower()
    if ext:
        return ext[1:]  # 去掉点
    else:
        return 'no_ext'

def collect_token_stats(root_dir):
    records = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            # 只统计文本相关文件
            if filename.endswith(('.txt', '.py', '.md', '.csv', '.json', '.log')):
                token_count = count_tokens_in_file(file_path)
                file_type = get_file_type(filename)
                records.append({
                    'File': file_path,
                    'FileType': file_type,
                    'Tokens': token_count
                })
    return pd.DataFrame(records)

def print_crystal_table(df):
    # 按文件类型分组，组内按token降序
    df_sorted = df.sort_values(['FileType', 'Tokens'], ascending=[True, False])
    grouped = df_sorted.groupby('FileType')

    print("\n========== Crystal Table: Token Count by File Type ==========\n")
    for file_type, group in grouped:
        print(f"\n--- File Type: {file_type} ---")
        print(tabulate(
            group[['File', 'Tokens']].sort_values('Tokens', ascending=False),
            headers=['File', 'Tokens'],
            tablefmt='fancy_grid',
            showindex=False
        ))

    print("\n========== Summary by File Type ==========\n")
    summary = df.groupby('FileType')['Tokens'].sum().reset_index().sort_values('Tokens', ascending=False)
    print(tabulate(
        summary,
        headers=['FileType', 'Total Tokens'],
        tablefmt='fancy_grid',
        showindex=False
    ))

    print("\n========== Grand Total ==========\n")
    print(f"All files total tokens: {df['Tokens'].sum()}")
    print("\n=============  End  =============\n")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("请在命令行中指定要统计的目录路径，例如：")
        print('python main.py "C:\\Users\\<USER>\\My Folder With Spaces"')
        sys.exit(1)

    input_path = sys.argv[1].strip()
    abs_path = os.path.abspath(os.path.normpath(input_path))

    if not os.path.exists(abs_path):
        print(f"指定的路径不存在：{abs_path}")
        sys.exit(1)
    if not os.path.isdir(abs_path):
        print(f"指定的路径不是一个目录：{abs_path}")
        sys.exit(1)

    print(f"正在统计目录及其所有子目录下的 token 数量：{abs_path}\n")

    df = collect_token_stats(abs_path)
    if df.empty:
        print("未找到可统计的文件。")
        sys.exit(0)

    print_crystal_table(df)
